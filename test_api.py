#!/usr/bin/env python3
"""
API测试脚本
"""
import requests
import json

# 基础URL
BASE_URL = "http://localhost:8002/api"

def login():
    """登录获取token"""
    url = f"{BASE_URL}/v1/auth/login"
    data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"登录请求状态码: {response.status_code}")
        print(f"登录响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            return result.get("access_token")
        else:
            print(f"登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"登录请求异常: {e}")
        return None

def test_departments(token):
    """测试部门API"""
    url = f"{BASE_URL}/v1/departments/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"部门列表请求状态码: {response.status_code}")
        print(f"部门列表响应: {response.text}")
        
        if response.status_code == 200:
            departments = response.json()
            print(f"获取到 {len(departments)} 个部门")
            for dept in departments:
                print(f"- {dept.get('name')}: {dept.get('resident_count', 0)} 个住户")
        else:
            print(f"获取部门列表失败: {response.text}")
    except Exception as e:
        print(f"部门列表请求异常: {e}")

def test_frontend_proxy():
    """测试前端代理"""
    url = "http://localhost:3001/api/v1/auth/login"
    data = {
        "username": "admin",
        "password": "admin123"
    }

    try:
        response = requests.post(url, json=data)
        print(f"前端代理登录请求状态码: {response.status_code}")
        print(f"前端代理登录响应: {response.text}")

        if response.status_code == 200:
            result = response.json()
            return result.get("access_token")
        else:
            print(f"前端代理登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"前端代理登录请求异常: {e}")
        return None

def test_departments_via_proxy(token):
    """通过前端代理测试部门API"""
    url = "http://localhost:3001/api/v1/departments/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    try:
        response = requests.get(url, headers=headers)
        print(f"前端代理部门列表请求状态码: {response.status_code}")
        print(f"前端代理部门列表响应: {response.text}")

        if response.status_code == 200:
            departments = response.json()
            print(f"通过前端代理获取到 {len(departments)} 个部门")
            for dept in departments:
                print(f"- {dept.get('name')}: {dept.get('resident_count', 0)} 个住户")
        else:
            print(f"通过前端代理获取部门列表失败: {response.text}")
    except Exception as e:
        print(f"前端代理部门列表请求异常: {e}")

def main():
    """主函数"""
    print("开始测试API...")

    # 测试直接后端API
    print("\n=== 测试直接后端API ===")
    token = login()
    if not token:
        print("无法获取token，测试终止")
        return

    print(f"获取到token: {token[:50]}...")
    test_departments(token)

    # 测试前端代理
    print("\n=== 测试前端代理API ===")
    proxy_token = test_frontend_proxy()
    if proxy_token:
        print(f"通过代理获取到token: {proxy_token[:50]}...")
        test_departments_via_proxy(proxy_token)
    else:
        print("无法通过代理获取token")

if __name__ == "__main__":
    main()
